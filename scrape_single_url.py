import asyncio
import csv
import os
import random
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from datetime import datetime
import re
from urllib.parse import urljoin

class SingleURLScraper:
    def __init__(self):
        self.base_url = "https://tunecaster.com"
    
    async def scrape_single_chart(self, url, max_retries=3):
        """Scrape a single chart URL"""
        
        for attempt in range(max_retries):
            try:
                print(f"Attempt {attempt + 1}/{max_retries} for {url}")

                async with async_playwright() as p:
                    browser = await p.chromium.launch(headless=True)

                    user_agents = [
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                    ]

                    context = await browser.new_context(
                        user_agent=random.choice(user_agents),
                        viewport={'width': random.randint(1200, 1920), 'height': random.randint(800, 1080)},
                        locale='en-US',
                        timezone_id='America/New_York'
                    )

                    page = await context.new_page()

                    try:
                        print(f"Navigating to {url}...")
                        await asyncio.sleep(random.uniform(1, 3))
                        await page.goto(url, wait_until='domcontentloaded', timeout=45000)
                        await asyncio.sleep(random.uniform(2, 4))

                        # Check if page loaded properly
                        title = await page.title()
                        if not title or 'error' in title.lower() or 'not found' in title.lower():
                            raise Exception(f"Page seems to have loading issues: {title}")

                        html_content = await page.content()
                        chart_data = self.parse_chart(html_content, url)

                        if chart_data and len(chart_data['records']) > 0:
                            print(f"Successfully scraped {len(chart_data['records'])} records")
                            return chart_data
                        else:
                            raise Exception("No valid chart data found")

                    except Exception as e:
                        print(f"Attempt {attempt + 1} failed: {e}")
                        if attempt < max_retries - 1:
                            wait_time = random.uniform(5, 15) * (attempt + 1)
                            print(f"Waiting {wait_time:.1f} seconds before retry...")
                            await asyncio.sleep(wait_time)
                        else:
                            print(f"All {max_retries} attempts failed for {url}")
                            return None
                    finally:
                        await browser.close()

            except Exception as e:
                print(f"Browser launch failed on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(random.uniform(3, 8))

        return None
    
    def parse_chart(self, html_content, url):
        soup = BeautifulSoup(html_content, 'html.parser')
        songs = self.extract_songs_from_html(soup)
        
        chart_date = self.extract_chart_date_from_page(soup)
        
        if chart_date is None:
            chart_date = self.extract_chart_date_from_url(url)
            
        if chart_date is None:
            print(f"Skipping chart due to invalid date: {url}")
            return None
        
        # Determine chart type from URL
        chart_type = 'pop'  # Default to pop for week URLs
        if 'rock' in url:
            chart_type = 'rock'
            
        records = []
        
        for song in songs:
            record = {
                "chart_date": chart_date,
                "chart_type": chart_type,
                "rank": song['position'],
                "title": song['title'],
                "artist": song['artist'],
                "url": url
            }
            records.append(record)
        
        return {
            'chart_info': {
                'chart_type': chart_type,
                'chart_date': chart_date,
                'url': url
            },
            'records': records
        }
    
    def extract_songs_from_html(self, soup):
        songs = []
        songs.extend(self.extract_using_table_structure(soup))
        
        text_songs = self.extract_using_sequential_parsing(soup)
        for song in text_songs:
            if not any(s['position'] == song['position'] for s in songs):
                songs.append(song)
        
        unique_songs = self.clean_songs(songs)
        unique_songs.sort(key=lambda x: x.get('position', 999))
        
        return unique_songs
    
    def extract_using_table_structure(self, soup):
        songs = []
        tables = soup.find_all('table', class_='t2')
        
        i = 0
        while i < len(tables):
            table = tables[i]
            tw_cell = table.find('td', class_='thisWeek')
            title_cell = table.find('td', class_='title20') or table.find('td', class_='titleBoth20')
            
            if tw_cell and title_cell:
                tw_text = tw_cell.get_text().strip()
                
                if tw_text == 'TW' or not tw_text.isdigit():
                    i += 1
                    continue
                
                tw_position = int(tw_text)
                title = self.extract_title_from_cell(title_cell)
                artist = self.find_artist_in_next_tables(tables, i + 1)
                
                if title:
                    if not isinstance(artist, str):
                        artist = str(artist) if artist else ""

                    artist_name = self.parse_multiple_artists(artist)
                    songs.append({
                        'position': tw_position,
                        'title': title,
                        'artist': artist_name
                    })
            
            i += 1
        
        return songs
    
    def extract_using_sequential_parsing(self, soup):
        songs = []
        page_text = soup.get_text()
        lines = [line.strip() for line in page_text.split('\n') if line.strip()]
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            if any(skip in line.lower() for skip in ['download', 'amazon', 'img', 'src=', 'http', '![]']):
                i += 1
                continue
            
            # Look for standard chart format: number | number | title
            standard_match = re.match(r'^(\d+)\s*\|\s*(\d+)\s*\|\s*([^|]*)', line)
            if standard_match:
                tw_position = int(standard_match.group(2))
                title = standard_match.group(3).strip()
                
                if not title or len(title.strip()) < 2:
                    title = self.find_title_in_next_lines(lines, i + 1)
                
                artist = self.find_artist_in_text_lines(lines, i + 1)
                
                if title and len(title.strip()) >= 2:
                    if not isinstance(artist, str):
                        artist = str(artist) if artist else ""

                    artist_name = self.parse_multiple_artists(artist)
                    songs.append({
                        'position': tw_position,
                        'title': title,
                        'artist': artist_name
                    })
                i += 1
                continue
            
            i += 1
        
        return songs
    
    def find_title_in_next_lines(self, lines, start_index):
        for j in range(start_index, min(start_index + 3, len(lines))):
            if j >= len(lines):
                break
            
            line = lines[j].strip()
            
            if (not line or 
                any(skip in line.lower() for skip in ['download', 'amazon', 'img', 'src=', 'http', '![]']) or
                re.match(r'^[\|\s\-]*$', line) or
                re.match(r'^\d+\s*\|\s*\d+', line) or
                line.isdigit()):
                continue
            
            clean_line = re.sub(r'^[\|\s]+|[\|\s]+$', '', line)
            clean_line = re.sub(r'\s+', ' ', clean_line).strip()
            
            if clean_line and len(clean_line) >= 2:
                return clean_line
        
        return ""
    
    def find_artist_in_text_lines(self, lines, start_index):
        for j in range(start_index, min(start_index + 12, len(lines))):
            if j >= len(lines):
                break
            
            line = lines[j].strip()
            
            if (not line or 
                any(skip in line.lower() for skip in ['download', 'amazon', 'img', 'src=', 'http', '![]']) or
                re.match(r'^[\|\s\-]*$', line) or
                re.match(r'^(\d+|\-)\s*\|\s*(\d+)', line) or
                line.isdigit() or
                len(line) > 150 or
                line.startswith('[') or
                '../../' in line or
                'week' in line.lower() or
                'chart' in line.lower()):
                continue
            
            clean_line = re.sub(r'^[\|\s]+|[\|\s]+$', '', line)
            clean_line = re.sub(r'\s+', ' ', clean_line).strip()
            
            if (clean_line and 
                len(clean_line) >= 2 and 
                len(clean_line) < 150 and
                any(c.isalpha() for c in clean_line) and
                not clean_line.isdigit()):
                
                if (not any(word in clean_line.lower() for word in ['peak', 'week', 'chart', 'html']) and
                    not re.match(r'^\d+[\s\|]', clean_line)):
                    return clean_line
        
        return ""
    
    def extract_chart_date_from_page(self, soup):
        try:
            month_names = {
                'january': 1, 'jan': 1, 'february': 2, 'feb': 2, 'march': 3, 'mar': 3,
                'april': 4, 'apr': 4, 'may': 5, 'june': 6, 'jun': 6,
                'july': 7, 'jul': 7, 'august': 8, 'aug': 8,
                'september': 9, 'sep': 9, 'sept': 9, 'october': 10, 'oct': 10,
                'november': 11, 'nov': 11, 'december': 12, 'dec': 12
            }
            
            # Check headings first
            headings = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            for heading in headings:
                heading_text = heading.get_text().strip()
                
                date_patterns = [
                    r'for\s+([A-Za-z]+)\s+(\d{1,2}),?\s+(\d{4})',
                    r'([A-Za-z]+)\s+(\d{1,2}),?\s+(\d{4})',
                    r'(\d{1,2})\s+([A-Za-z]+)\s+(\d{4})',
                ]
                
                for pattern in date_patterns:
                    date_match = re.search(pattern, heading_text, re.IGNORECASE)
                    if date_match:
                        groups = date_match.groups()
                        
                        try:
                            if len(groups) == 3 and groups[0].lower() in month_names:
                                month_str = groups[0].lower()
                                day = int(groups[1])
                                year = int(groups[2])
                                month = month_names[month_str]
                                
                                parsed_date = datetime(year, month, day)
                                return parsed_date.strftime('%Y-%m-%d')
                                
                        except (ValueError, KeyError):
                            continue
            
        except Exception as e:
            print(f"Error extracting date from page: {e}")
        
        return None
    
    def extract_chart_date_from_url(self, url):
        match = re.search(r'/charts/(\d{2})/(?:rock|week)(\d{4})\.html', url)
        if match:
            decade = int(match.group(1))
            week_number = match.group(2)
            year_suffix = int(week_number[:2])
            week = int(week_number[2:])
            
            # Determine full year based on decade
            if decade >= 60 and decade <= 99:
                full_year = 1900 + year_suffix
            elif decade >= 0 and decade <= 20:
                full_year = 2000 + year_suffix
            else:
                full_year = 1900 + year_suffix if year_suffix >= 60 else 2000 + year_suffix
            
            try:
                from datetime import date, timedelta
                jan_1 = date(full_year, 1, 1)
                days_to_monday = (7 - jan_1.weekday()) % 7
                first_monday = jan_1 + timedelta(days=days_to_monday)
                week_start = first_monday + timedelta(weeks=week-1)
                return week_start.strftime('%Y-%m-%d')
            except (ValueError, OverflowError):
                return f"{full_year}-01-01"
        
        print(f"Error: Could not extract date from URL: {url}")
        return None
        
    def extract_title_from_cell(self, title_cell):
        link = title_cell.find('a', class_='songLink')
        if link:
            return link.get_text().strip()
        
        title = title_cell.get_text().strip()
        return re.sub(r'\s+', ' ', title).strip() if title else ""
    
    def find_artist_in_next_tables(self, tables, start_index):
        for j in range(start_index, min(start_index + 20, len(tables))):
            if j >= len(tables):
                break
            
            table = tables[j]
            artist_cell = table.find('td', class_='artist20')
            
            if artist_cell:
                artist = self.extract_artist_from_cell(artist_cell)
                if artist:
                    return artist
            
            # Look in all cells if no artist cell found
            all_cells = table.find_all('td')
            for cell in all_cells:
                cell_text = cell.get_text().strip()
                
                if not cell_text or len(cell_text) < 2:
                    continue
                
                if (cell_text.isdigit() or 
                    cell_text in ['-', '|'] or
                    any(skip in cell_text.lower() for skip in ['download', 'youtube', 'amazon', 'http', '../../', 'week', 'chart', 'peak', 'html', 'img']) or
                    cell_text.startswith('[') or
                    re.match(r'^\d+\s*\|\s*\d+', cell_text)):
                    continue
                
                if (len(cell_text) > 1 and len(cell_text) < 200 and
                    any(c.isalpha() for c in cell_text)):
                    return cell_text
        
        return ""
    
    def extract_artist_from_cell(self, artist_cell):
        artist_links = artist_cell.find_all('a', class_='artistLink')
        
        if artist_links:
            artists = []
            for link in artist_links:
                artist_name = link.get_text().strip()
                if artist_name and len(artist_name) > 1:
                    artists.append(artist_name)
            
            if len(artists) > 1:
                return ' with '.join(artists)
            elif len(artists) == 1:
                return artists[0]
        
        artist_text = artist_cell.get_text().strip()
        return re.sub(r'\s+', ' ', artist_text).strip() if artist_text else ""
    
    def parse_multiple_artists(self, artist_text):
        if not artist_text:
            return ""

        if isinstance(artist_text, str):
            return artist_text.strip()

        if isinstance(artist_text, list):
            return ', '.join(str(item).strip() for item in artist_text if str(item).strip())

        return str(artist_text).strip()
    
    def clean_songs(self, songs):
        seen_positions = set()
        unique_songs = []
        
        for song in songs:
            if not song or not song.get('position'):
                continue
            
            position = song['position']
            if position in seen_positions:
                continue
            
            title = song.get('title', '').strip()
            artist_data = song.get('artist', '')
            
            if not title:
                continue
            
            if isinstance(artist_data, str):
                artist_name = artist_data
            elif isinstance(artist_data, list):
                artist_name = ', '.join(str(item) for item in artist_data if str(item).strip())
            else:
                artist_name = str(artist_data) if artist_data else ""

            unique_songs.append({
                'position': position,
                'title': title,
                'artist': artist_name
            })
            
            seen_positions.add(position)
        
        return unique_songs
    
    def append_to_csv(self, chart_data, csv_file='data/charts_data.csv'):
        """Append new chart data to existing CSV file"""
        try:
            os.makedirs('data', exist_ok=True)
            
            # Check if file exists to determine if we need headers
            file_exists = os.path.exists(csv_file)
            
            with open(csv_file, 'a', newline='', encoding='utf-8') as f:
                fieldnames = ['chart_date', 'chart_type', 'rank', 'title', 'artist', 'url']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                
                # Write header if file is new
                if not file_exists:
                    writer.writeheader()
                
                # Write records
                for record in chart_data['records']:
                    writer.writerow(record)
                
            print(f"Successfully added {len(chart_data['records'])} records to {csv_file}")
            
        except Exception as e:
            print(f"Error writing to CSV: {e}")

async def main():
    url = "https://tunecaster.com/charts/50/week5952.html"
    
    scraper = SingleURLScraper()
    print(f"Scraping URL: {url}")
    
    chart_data = await scraper.scrape_single_chart(url)
    
    if chart_data:
        print(f"\nChart Date: {chart_data['chart_info']['chart_date']}")
        print(f"Chart Type: {chart_data['chart_info']['chart_type']}")
        print(f"Records found: {len(chart_data['records'])}")
        
        # Show first few records
        print("\nFirst 5 records:")
        for i, record in enumerate(chart_data['records'][:5], 1):
            print(f"  {record['rank']}. {record['title']} - {record['artist']}")
        
        # Append to CSV
        scraper.append_to_csv(chart_data)
        
    else:
        print("Failed to scrape the URL")

if __name__ == "__main__":
    asyncio.run(main())
